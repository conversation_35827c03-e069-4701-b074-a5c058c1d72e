# IPv4 to IPv6 Translation Configuration for Tailscale
# This configuration enables translation of IPv4 traffic to IPv6 addresses
# in remote Tailscale subnets, solving subnet overlap issues for industrial software.

# Enable or disable IPv4 to IPv6 translation
enabled: true

# IPv4 subnet that should be intercepted for translation
# All traffic to this subnet will be checked for translation
ipv4_subnet: "*************/24"

# Mappings from IPv4 addresses to IPv6 addresses
# The IPv6 addresses should be reachable via Tailscale (4via6 addresses)
mappings:
  # Example mappings - replace with your actual addresses
  "*************": "fd7a:115c:a1e0::1234:5678"
  "*************": "fd7a:115c:a1e0::1234:5679"
  "*************": "fd7a:115c:a1e0::1234:567a"
  "*************0": "fd7a:115c:a1e0::abcd:ef01"
  "**************": "fd7a:115c:a1e0::abcd:ef02"

# Notes:
# 1. The IPv4 addresses must be within the specified ipv4_subnet
# 2. The IPv6 addresses should be reachable via Tailscale
# 3. Industrial software can use the IPv4 addresses to communicate with IPv6 devices
# 4. Connection state is maintained automatically for bidirectional communication
